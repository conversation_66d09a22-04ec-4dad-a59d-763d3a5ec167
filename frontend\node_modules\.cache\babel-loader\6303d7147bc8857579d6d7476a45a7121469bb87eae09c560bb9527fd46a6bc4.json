{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('authToken'));\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const initAuth = () => {\n      const storedToken = localStorage.getItem('authToken');\n      const storedUser = localStorage.getItem('userData');\n      if (storedToken && storedUser) {\n        try {\n          // Simply restore user from localStorage without backend verification\n          setUser(JSON.parse(storedUser));\n          setToken(storedToken);\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n          setUser(null);\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    initAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        // Include cookies for session\n        body: JSON.stringify({\n          action: 'login',\n          email,\n          password\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        localStorage.setItem('authToken', data.token);\n        localStorage.setItem('userData', JSON.stringify(data.user));\n        setUser(data.user);\n        setToken(data.token);\n        return {\n          success: true,\n          user: data.user\n        };\n      } else {\n        return {\n          success: false,\n          message: data.message\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const register = async (name, email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        // Include cookies for session\n        body: JSON.stringify({\n          action: 'register',\n          name,\n          email,\n          password\n        })\n      });\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      // Call logout endpoint to clear server session\n      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=logout', {\n        method: 'GET',\n        credentials: 'include'\n      });\n    } catch (error) {\n      console.log('Logout API call failed, but continuing with local logout');\n    }\n\n    // Clear local storage\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    setUser(null);\n    setToken(null);\n  };\n  const forgotPassword = async email => {\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/forgot-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email\n        })\n      });\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const resetPassword = async (token, newPassword) => {\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/reset-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          token,\n          newPassword\n        })\n      });\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error. Please try again.'\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    forgotPassword,\n    resetPassword,\n    isAuthenticated: !!user,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"OpfcLSUH+QvV1lR1/bshprk98VE=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "initAuth", "storedToken", "storedUser", "JSON", "parse", "error", "console", "removeItem", "login", "email", "password", "response", "fetch", "method", "headers", "credentials", "body", "stringify", "action", "data", "json", "success", "setItem", "message", "register", "name", "logout", "log", "forgotPassword", "resetPassword", "newPassword", "value", "isAuthenticated", "isAdmin", "role", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('authToken'));\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initAuth = () => {\n      const storedToken = localStorage.getItem('authToken');\n      const storedUser = localStorage.getItem('userData');\n\n      if (storedToken && storedUser) {\n        try {\n          // Simply restore user from localStorage without backend verification\n          setUser(JSON.parse(storedUser));\n          setToken(storedToken);\n        } catch (error) {\n          console.error('Error parsing stored user data:', error);\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n          setUser(null);\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include', // Include cookies for session\n        body: JSON.stringify({\n          action: 'login',\n          email,\n          password\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        localStorage.setItem('authToken', data.token);\n        localStorage.setItem('userData', JSON.stringify(data.user));\n        setUser(data.user);\n        setToken(data.token);\n        return { success: true, user: data.user };\n      } else {\n        return { success: false, message: data.message };\n      }\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const register = async (name, email, password) => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include', // Include cookies for session\n        body: JSON.stringify({\n          action: 'register',\n          name,\n          email,\n          password\n        })\n      });\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // Call logout endpoint to clear server session\n      await fetch('http://localhost/react-news/frontend/src/pages/admin/simple_api.php?action=logout', {\n        method: 'GET',\n        credentials: 'include'\n      });\n    } catch (error) {\n      console.log('Logout API call failed, but continuing with local logout');\n    }\n\n    // Clear local storage\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('userData');\n    setUser(null);\n    setToken(null);\n  };\n\n  const forgotPassword = async (email) => {\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/forgot-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email\n        })\n      });\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const resetPassword = async (token, newPassword) => {\n    try {\n      const response = await fetch('http://localhost:5000/api/auth/reset-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          token,\n          newPassword\n        })\n      });\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      return { success: false, message: 'Network error. Please try again.' };\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    forgotPassword,\n    resetPassword,\n    isAuthenticated: !!user,\n    isAdmin: user?.role === 'admin'\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAACgB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;EACrE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMmB,QAAQ,GAAGA,CAAA,KAAM;MACrB,MAAMC,WAAW,GAAGL,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACrD,MAAMK,UAAU,GAAGN,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAEnD,IAAII,WAAW,IAAIC,UAAU,EAAE;QAC7B,IAAI;UACF;UACAT,OAAO,CAACU,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC,CAAC;UAC/BP,QAAQ,CAACM,WAAW,CAAC;QACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvDT,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;UACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;UACnCd,OAAO,CAAC,IAAI,CAAC;UACbE,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF;MACAI,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;QAClGC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,WAAW,EAAE,SAAS;QAAE;QACxBC,IAAI,EAAEb,IAAI,CAACc,SAAS,CAAC;UACnBC,MAAM,EAAE,OAAO;UACfT,KAAK;UACLC;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMS,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBzB,YAAY,CAAC0B,OAAO,CAAC,WAAW,EAAEH,IAAI,CAACzB,KAAK,CAAC;QAC7CE,YAAY,CAAC0B,OAAO,CAAC,UAAU,EAAEnB,IAAI,CAACc,SAAS,CAACE,IAAI,CAAC3B,IAAI,CAAC,CAAC;QAC3DC,OAAO,CAAC0B,IAAI,CAAC3B,IAAI,CAAC;QAClBG,QAAQ,CAACwB,IAAI,CAACzB,KAAK,CAAC;QACpB,OAAO;UAAE2B,OAAO,EAAE,IAAI;UAAE7B,IAAI,EAAE2B,IAAI,CAAC3B;QAAK,CAAC;MAC3C,CAAC,MAAM;QACL,OAAO;UAAE6B,OAAO,EAAE,KAAK;UAAEE,OAAO,EAAEJ,IAAI,CAACI;QAAQ,CAAC;MAClD;IACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACd,OAAO;QAAEgB,OAAO,EAAE,KAAK;QAAEE,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAAA,CAAOC,IAAI,EAAEhB,KAAK,EAAEC,QAAQ,KAAK;IAChD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;QAClGC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,WAAW,EAAE,SAAS;QAAE;QACxBC,IAAI,EAAEb,IAAI,CAACc,SAAS,CAAC;UACnBC,MAAM,EAAE,UAAU;UAClBO,IAAI;UACJhB,KAAK;UACLC;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMS,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAClC,OAAOD,IAAI;IACb,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd,OAAO;QAAEgB,OAAO,EAAE,KAAK;QAAEE,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMG,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF;MACA,MAAMd,KAAK,CAAC,mFAAmF,EAAE;QAC/FC,MAAM,EAAE,KAAK;QACbE,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACqB,GAAG,CAAC,0DAA0D,CAAC;IACzE;;IAEA;IACA/B,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;IACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;IACnCd,OAAO,CAAC,IAAI,CAAC;IACbE,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMiC,cAAc,GAAG,MAAOnB,KAAK,IAAK;IACtC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,gDAAgD,EAAE;QAC7EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDE,IAAI,EAAEb,IAAI,CAACc,SAAS,CAAC;UACnBR;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMU,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAClC,OAAOD,IAAI;IACb,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd,OAAO;QAAEgB,OAAO,EAAE,KAAK;QAAEE,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMM,aAAa,GAAG,MAAAA,CAAOnC,KAAK,EAAEoC,WAAW,KAAK;IAClD,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDE,IAAI,EAAEb,IAAI,CAACc,SAAS,CAAC;UACnBvB,KAAK;UACLoC;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMX,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAClC,OAAOD,IAAI;IACb,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd,OAAO;QAAEgB,OAAO,EAAE,KAAK;QAAEE,OAAO,EAAE;MAAmC,CAAC;IACxE;EACF,CAAC;EAED,MAAMQ,KAAK,GAAG;IACZvC,IAAI;IACJE,KAAK;IACLI,OAAO;IACPU,KAAK;IACLgB,QAAQ;IACRE,MAAM;IACNE,cAAc;IACdC,aAAa;IACbG,eAAe,EAAE,CAAC,CAACxC,IAAI;IACvByC,OAAO,EAAE,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,IAAI,MAAK;EAC1B,CAAC;EAED,oBACEnD,OAAA,CAACC,WAAW,CAACmD,QAAQ;IAACJ,KAAK,EAAEA,KAAM;IAAAzC,QAAA,EAChCA;EAAQ;IAAA8C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAChD,GAAA,CA9JWF,YAAY;AAAAmD,EAAA,GAAZnD,YAAY;AAgKzB,eAAeL,WAAW;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}